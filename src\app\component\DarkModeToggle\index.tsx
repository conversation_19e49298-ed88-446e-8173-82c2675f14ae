"use client";

import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/app/lib/redux/store";
import { toggleDarkMode } from "../NavBar/redux/darkModeSlice";

export default function DarkModeToggle() {
  const isDark = useSelector((state: RootState) => state.darkMode.value);
  const dispatch = useDispatch();
  const handleToggle = () => {
    dispatch(toggleDarkMode());
  };
  console.log("isDark", isDark);

   

  return (
    <button
      onClick={handleToggle}
      className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-gray-700"
      role="switch"
      aria-checked={isDark}
      aria-label="Toggle dark mode"
    >
      <span
  className={`
    inline-block h-4 w-4 transform rounded-full bg-white transition-transform
    ${isDark ? "translate-x-6 rtl:-translate-x-6" : "translate-x-1 rtl:-translate-x-1"}
  `}
/>
      <span className="sr-only">Toggle dark mode</span>
    </button>
  );
}
