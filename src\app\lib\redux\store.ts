import { configureStore } from "@reduxjs/toolkit";

// Example slice (replace with your actual reducer)
import darkModeReducer from "../../component/NavBar/redux/darkModeSlice";

export const store = configureStore({
  reducer: {
    darkMode: darkModeReducer, // Add your reducers here
  },
});

// Infer the `RootState` and `AppDispatch` types from the store
export type RootState  = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;