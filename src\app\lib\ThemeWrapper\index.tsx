"use client";

import { useEffect } from "react";
import { useSelector } from "react-redux";
import type { RootState } from "../redux/store";

export default function ThemeWrapper({ children }: { children: React.ReactNode }) {
  const isDark = useSelector((state: RootState) => state.darkMode.value);
console.log("isDark", isDark);

  useEffect(() => {
    if (isDark) {
      document.documentElement.classList.add("dark"); // add to <html>
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [isDark]);

  return <>{children}</>;
}
