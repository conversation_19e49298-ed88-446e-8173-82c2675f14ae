import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Theme utility functions
export const themeClasses = {
  // Background variants
  background: {
    default: "bg-background text-foreground",
    card: "bg-card text-card-foreground",
    muted: "bg-muted text-muted-foreground",
    accent: "bg-accent text-accent-foreground",
  },
  
  // Button variants
  button: {
    primary: "bg-primary text-primary-foreground hover:bg-primary/90",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
    outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
    ghost: "hover:bg-accent hover:text-accent-foreground",
  },
  
  // Border variants
  border: {
    default: "border-border",
    input: "border-input",
    ring: "ring-ring",
  },
  
  // Text variants
  text: {
    default: "text-foreground",
    muted: "text-muted-foreground",
    primary: "text-primary",
    secondary: "text-secondary-foreground",
  }
};

// Helper function to get theme classes
export function getThemeClass(category: keyof typeof themeClasses, variant: string) {
  return themeClasses[category]?.[variant as keyof typeof themeClasses[typeof category]] || "";
}
