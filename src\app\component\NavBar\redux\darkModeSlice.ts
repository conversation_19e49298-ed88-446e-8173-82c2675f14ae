import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  value: false, // Start in light mode
};

export const darkModeSlice = createSlice({
  name: "darkMode",
  initialState,
  reducers: {
    toggleDarkMode: (state) => {
      state.value = !state.value; // Toggles between true/false
    },
  },
});

// Export actions
export const { toggleDarkMode } = darkModeSlice.actions;

// Export reducer
export default darkModeSlice.reducer;