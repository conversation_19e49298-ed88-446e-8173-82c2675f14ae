import { doc, getDoc } from "firebase/firestore";
import { db } from "../lib/firebase/firebase";
import { getTranslations } from "next-intl/server";

export default async function ContactPage() {
  const t = await getTranslations("contact");

  const docRef = doc(db, "content", "contact");
  const docSnap = await getDoc(docRef);
  const contactData = docSnap.exists() ? docSnap.data() : null;

  return (
    <div className="w-full h-screen flex items-center justify-center bg-background p-4">
  <div className="w-full max-w-lg bg-white dark:bg-gray-800 shadow-lg rounded-2xl p-8">
    <h1 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">
      {t("title")}
    </h1>

    <div className="space-y-3 text-gray-700 dark:text-gray-300">
      <p>
        <span className="font-medium">{t("email")}:</span> {contactData?.email}
      </p>
      <p>
        <span className="font-medium">{t("phone")}:</span> {contactData?.phone}
      </p>
      <p>
        <span className="font-medium">{t("whatsapp")}:</span> {contactData?.whatsapp}
      </p>
      <p>
        <span className="font-medium">{t("facebook")}:</span>{" "}
        <a
          href={contactData?.facebook}
          target="_blank"
          className="text-blue-500 hover:underline"
        >
          {contactData?.facebook}
        </a>
      </p>
      <p>
        <span className="font-medium">{t("instagram")}:</span>{" "}
        <a
          href={contactData?.instagram}
          target="_blank"
          className="text-pink-500 hover:underline"
        >
          {contactData?.instagram}
        </a>
      </p>
      <p>
        <span className="font-medium">{t("linkedin")}:</span>{" "}
        <a
          href={contactData?.linkedin}
          target="_blank"
          className="text-blue-600 hover:underline"
        >
          {contactData?.linkedin}
        </a>
      </p>
    </div>
  </div>
</div>

  );
}
