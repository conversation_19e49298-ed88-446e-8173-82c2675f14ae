import { NextRequest, NextResponse } from 'next/server';
import { defaultLocale, locales, type Locale } from './app/lib/i18n/config';

export function middleware(request: NextRequest) {
  // Get the current locale from cookie or Accept-Language header
  const cookieLocale = request.cookies.get('locale')?.value as Locale;
  const acceptLanguage = request.headers.get('accept-language');
  
  // Determine the best locale
  let locale: Locale = defaultLocale;
  
  if (cookieLocale && locales.includes(cookieLocale)) {
    locale = cookieLocale;
  } else if (acceptLanguage) {
    // Simple language detection from Accept-Language header
    const preferredLocale = acceptLanguage
      .split(',')[0]
      .split('-')[0] as Locale;
    
    if (locales.includes(preferredLocale)) {
      locale = preferredLocale;
    }
  }
  
  // Create response
  const response = NextResponse.next();
  
  // Set the locale cookie if it's different from current
  if (cookieLocale !== locale) {
    response.cookies.set('locale', locale, {
      maxAge: 365 * 24 * 60 * 60, // 1 year
      httpOnly: false, // Allow client-side access for language switcher
      sameSite: 'lax'
    });
  }
  
  // Set direction for RTL languages
  if (locale === 'ar') {
    response.headers.set('x-locale-direction', 'rtl');
  } else {
    response.headers.set('x-locale-direction', 'ltr');
  }
  
  return response;
}

export const config = {
  // Match all paths except static files and API routes
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'
  ]
};
