import React from "react";

interface ExampleCardProps {
  title: string;
  description: string;
  icon?: string;
}

export default function ExampleCard({ title, description, icon }: ExampleCardProps) {
  return (
    <div className="bg-card text-card-foreground border border-border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
      <div className="flex items-center gap-3 mb-3">
        {icon && <span className="text-2xl">{icon}</span>}
        <h3 className="text-lg font-semibold">{title}</h3>
      </div>
      <p className="text-muted-foreground text-sm leading-relaxed">{description}</p>
    </div>
  );
}
