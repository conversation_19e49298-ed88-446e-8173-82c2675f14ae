import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import NavBar from "./component/NavBar";
import Footer from "./component/Footer";
import Providers from "./lib/redux/Providers";
import ThemeWrapper from "./lib/ThemeWrapper";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { cookies } from 'next/headers';
import { defaultLocale, locales, type Locale } from './lib/i18n/config';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Get current locale from cookie
  const cookieStore = await cookies();
  const locale = cookieStore.get('locale')?.value as Locale || defaultLocale;
  const validLocale = locales.includes(locale) ? locale : defaultLocale;

  // Get messages for the current locale
  const messages = await getMessages();

  // Determine text direction for RTL languages
  const direction = validLocale === 'ar' ? 'rtl' : 'ltr';

  return (
    <html lang={validLocale} dir={direction}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <NextIntlClientProvider messages={messages}>
          <Providers>
            <ThemeWrapper>
              <NavBar />
              <main className="flex-1">
                {children}
              </main>
              <Footer />
            </ThemeWrapper>
          </Providers>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
