"use client";

import { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import type { RootState } from "../redux/store";
import { toggleDarkMode } from "../../component/NavBar/redux/darkModeSlice";

export default function ThemeWrapper({ children }: { children: React.ReactNode }) {
  const isDark = useSelector((state: RootState) => state.darkMode.value);
  const dispatch = useDispatch();
  console.log("isDark", isDark);

  // Initialize theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('darkMode');
    if (savedTheme !== null) {
      const savedIsDark = JSON.parse(savedTheme);
      if (savedIsDark !== isDark) {
        dispatch(toggleDarkMode());
      }
    }
  }, [dispatch, isDark]);

  // Apply theme to document and save to localStorage
  useEffect(() => {
    if (isDark) {
      document.documentElement.classList.add("dark"); // add to <html>
    } else {
      document.documentElement.classList.remove("dark");
    }

    // Save to localStorage
    localStorage.setItem('darkMode', JSON.stringify(isDark));
  }, [isDark]);

  return <>{children}</>;
}
